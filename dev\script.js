// Global function to open contact form - accessible from anywhere
function openContactForm() {
    const contactOverlay = document.getElementById('contact-overlay');
    if (contactOverlay) {
        contactOverlay.classList.add('active');
        document.body.style.overflow = 'hidden'; // Prevent background scrolling
    }
}

document.addEventListener('DOMContentLoaded', () => {
    const loadingScreen = document.getElementById('loading-screen');
    const loadingBar = document.getElementById('loading-bar');
    const loadingPercentage = document.getElementById('loading-percentage');
    let progress = 0;
    const loadingInterval = setInterval(() => {
        progress += 1;
        // Ensure progress doesn't exceed 100 visually if interval runs fast
        const displayProgress = Math.min(progress, 100);
        loadingBar.style.width = `${displayProgress}%`;
        loadingPercentage.textContent = `${displayProgress}%`;

        if (progress >= 100) {
            clearInterval(loadingInterval);
            setTimeout(() => {
                loadingScreen.style.opacity = '0';
                // Use transitionend event for more reliable removal
                loadingScreen.addEventListener('transitionend', () => {
                   if (loadingScreen.style.opacity === '0') { // Check final state
                     loadingScreen.style.display = 'none';

                     // Initialize profile video after loading screen is dismissed
                     const profileVideo = document.getElementById('profile-video');
                     if (profileVideo) {
                         profileVideo.play().catch(e => console.log('Video play failed after loading:', e));
                     }
                   }
                }, { once: true }); // Remove listener after it runs once
            }, 300); // Wait a bit after 100%
        }
    }, 30); // Loading speed

    const themeToggle = document.getElementById('theme-toggle');

    // Force dark mode as default, regardless of saved preference
    // Remove light-mode class if it exists
    document.body.classList.remove('light-mode');
    // Save dark mode preference to localStorage
    localStorage.setItem('theme', 'dark');

    // Function to update particles based on theme
    function updateParticles() {
        const isDarkMode = !document.body.classList.contains('light-mode');
        const particleColor = isDarkMode ? "#ffffff" : "#000000";
        const bgColor = isDarkMode ? "#000000" : "#ffffff";

        tsParticles.load("tsparticles", {
            fpsLimit: 60,
            particles: {
                number: {
                    value: isDarkMode ? 80 : 100, // More particles in light mode for better visibility
                    density: {
                        enable: true,
                        value_area: 800
                    }
                },
                color: {
                    value: particleColor // Particle color based on theme
                },
                shape: {
                    type: "circle"
                },
                opacity: {
                    value: isDarkMode ? 0.5 : 0.8, // Higher opacity for light mode
                    random: true,
                    anim: {
                        enable: true,
                        speed: 0.5,
                        opacity_min: isDarkMode ? 0.1 : 0.3, // Higher minimum opacity for light mode
                        sync: false
                    }
                },
                size: {
                    value: isDarkMode ? 2 : 2.5, // Slightly larger particles for light mode
                    random: true,
                    anim: {
                        enable: false
                    }
                },
                line_linked: {
                    enable: false // Disable lines connecting particles
                },
                move: {
                    enable: true,
                    speed: 0.5, // Particle speed
                    direction: "none",
                    random: true,
                    straight: false,
                    out_mode: "out",
                    bounce: false,
                    attract: {
                        enable: false
                    }
                }
            },
            interactivity: {
                detect_on: "canvas",
                events: {
                    onhover: {
                        enable: false, // Disable hover effects
                    },
                    onclick: {
                        enable: false, // Disable click effects
                    },
                    resize: true
                }
            },
            detectRetina: true,
            background: {
               color: bgColor, // Background color based on theme
            }
        });
    }

    // Initialize particles based on current theme
    updateParticles();

    // Prevent context menu on footer images but allow hover
    const footerImages = document.querySelectorAll('.footer-image');
    const footer = document.querySelector('.footer');

    // Prevent context menu on images
    footerImages.forEach(image => {
        // Prevent right-click context menu
        image.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            return false;
        });

        // Remove pointer-events: none to allow hover
        image.style.pointerEvents = 'auto';
    });

    // Toggle theme with iOS-style switch animation
    if (themeToggle) {
        themeToggle.addEventListener('click', () => {
            // Prevent multiple clicks during animation
            if (document.body.classList.contains('theme-transitioning')) {
                return;
            }

            // Add transitioning class for animation
            document.body.classList.add('theme-transitioning');

            // Toggle light mode immediately for a more responsive feel
            document.body.classList.toggle('light-mode');

            // Save theme preference
            const isLightMode = document.body.classList.contains('light-mode');
            localStorage.setItem('theme', isLightMode ? 'light' : 'dark');

            // Update particles
            updateParticles();

            // Update Skycons color based on theme
            skycons.color = document.body.classList.contains('light-mode') ? "black" : "white";

            // Remove transitioning class after animation completes
            setTimeout(() => {
                document.body.classList.remove('theme-transitioning');
            }, 600); // Shorter duration for a snappier feel
        });
    }

    // --- Clock and Date ---
    function updateClock() {
        const now = new Date();
        const timeElement = document.getElementById('time');
        const dateElement = document.getElementById('date');

        if (timeElement && dateElement) { // Check if elements exist
            // Format time: HH:MM:SS (24-hour)
            const time = now.toLocaleTimeString('en-GB'); // 'en-GB' often gives HH:MM:SS

            // Format date: ShortWeekday, ShortMonth Day
            const date = now.toLocaleDateString('en-US', {
                weekday: 'short',
                month: 'short',
                day: 'numeric'
            });

            timeElement.textContent = time;
            dateElement.textContent = date;
        }
    }
    setInterval(updateClock, 1000);
    updateClock(); // Initial call

    // --- Weather Widget ---
    const weatherToggle = document.querySelector('.weather-toggle');
    const weatherWidget = document.querySelector('.weather-widget');
    let weatherVisible = false;
    let weatherCloseTimer = null; // Variable to hold the timeout ID

    // Function to handle closing the widget (to avoid repetition)
    function closeWeatherWidget() {
        if (!weatherVisible) return; // Already closed or closing

        weatherVisible = false;
        if (weatherCloseTimer) {
            clearTimeout(weatherCloseTimer); // Clear auto-close timer if manually closed
            weatherCloseTimer = null;
        }
        weatherWidget.classList.remove('active');
        // Listen for transition end to hide the element fully
        weatherWidget.addEventListener('transitionend', () => {
            if (!weatherWidget.classList.contains('active')) {
                weatherWidget.style.display = 'none';
            }
        }, { once: true });
    }

    // --- Spotify Widget ---
    const spotifyToggle = document.getElementById('spotify-toggle-button');
    const spotifyWidget = document.getElementById('spotify-widget');
    let spotifyVisible = false;
    let spotifyCloseTimer = null;

    function closeSpotifyWidget() {
        if (!spotifyVisible) return; // Already closed or closing

        spotifyVisible = false;
        if (spotifyCloseTimer) {
            clearTimeout(spotifyCloseTimer);
            spotifyCloseTimer = null;
        }
        spotifyWidget.classList.remove('active');
        // Listen for transition end to hide the element fully
        spotifyWidget.addEventListener('transitionend', () => {
            if (!spotifyWidget.classList.contains('active')) {
                // Keep the widget in the DOM but hidden visually
                // This allows the music to continue playing
                spotifyWidget.style.visibility = 'hidden';
                // Don't set display: none to keep the iframe active
            }
        }, { once: true });
    }

    if (weatherToggle && weatherWidget) { // Check if elements exist
        weatherToggle.addEventListener('click', () => {
            // Clear any existing timer when toggle is clicked
            if (weatherCloseTimer) {
                clearTimeout(weatherCloseTimer);
                weatherCloseTimer = null;
            }

            if (!weatherVisible) { // If opening the widget
                // Close spotify widget if open
                if (spotifyVisible) {
                    closeSpotifyWidget();
                }

                weatherVisible = true;
                weatherWidget.style.display = 'block';
                // Timeout needed to allow display:block before transition starts
                setTimeout(() => {
                    weatherWidget.classList.add('active');
                }, 10);
                fetchWeather(); // Fetch weather when opening

                // Start the auto-close timer
                weatherCloseTimer = setTimeout(() => {
                    console.log("Auto-closing weather widget after 4 seconds.");
                    closeWeatherWidget(); // Call the close function
                }, 4000); // 4000 milliseconds = 4 seconds

            } else { // If closing the widget manually
               closeWeatherWidget();
            }
        });
    }

    if (spotifyToggle && spotifyWidget) {
        spotifyToggle.addEventListener('click', () => {
            // Clear any existing timer when toggle is clicked
            if (spotifyCloseTimer) {
                clearTimeout(spotifyCloseTimer);
                spotifyCloseTimer = null;
            }

            if (!spotifyVisible) { // If opening the widget
                // Close weather widget if open
                if (weatherVisible) {
                    closeWeatherWidget();
                }

                spotifyVisible = true;
                // If the widget was just hidden but not removed from DOM (for continuous playback)
                if (spotifyWidget.style.visibility === 'hidden') {
                    spotifyWidget.style.visibility = 'visible';
                } else {
                    spotifyWidget.style.display = 'block';
                }
                // Timeout needed to allow display:block before transition starts
                setTimeout(() => {
                    spotifyWidget.classList.add('active');
                }, 10);

                // No auto-close for Spotify - let user enjoy the music
            } else { // If closing the widget manually
                closeSpotifyWidget();
            }
        });

        // Close on ESC key (same as contact form)
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && spotifyVisible) {
                closeSpotifyWidget();
            }
        });

        // Close when clicking anywhere on the document
        document.addEventListener('click', (event) => {
            // Only close if widget is visible and click is outside the widget and toggle button
            if (spotifyVisible &&
                !spotifyWidget.contains(event.target) &&
                event.target !== spotifyToggle &&
                !spotifyToggle.contains(event.target)) {
                closeSpotifyWidget();
            }
        });
    }

    // Instantiate Skycons with theme-aware color
    const skycons = new Skycons({
        "color": document.body.classList.contains('light-mode') ? "black" : "white"
    });

    async function fetchWeather() {
        const weatherInfo = document.querySelector('.weather-info');
        const forecastContainer = document.querySelector('.forecast-container');
        if (!weatherInfo || !forecastContainer) return;

        const apiKey = 'fb7daab92cf971a98f67b33cf44d3504'; // Replace YOUR_API_KEY
        const city = 'Montreal';
        const units = 'metric';
        const weatherUrl = `https://api.openweathermap.org/data/2.5/weather?q=${city}&units=${units}&appid=${apiKey}`;
        const forecastUrl = `https://api.openweathermap.org/data/2.5/forecast?q=${city}&units=${units}&appid=${apiKey}`;

        weatherInfo.innerHTML = '';
        forecastContainer.innerHTML = '';

        skycons.remove("weather-icon-canvas");
        for (let i = 0; i < 5; i++) {
            skycons.remove(`forecast-icon-${i}`);
        }

        try {
            const [weatherResponse, forecastResponse] = await Promise.all([
                fetch(weatherUrl),
                fetch(forecastUrl)
            ]);

            if (!weatherResponse.ok) throw new Error(`Weather fetch failed: ${weatherResponse.statusText}`);
            if (!forecastResponse.ok) throw new Error(`Forecast fetch failed: ${forecastResponse.statusText}`);

            const weatherData = await weatherResponse.json();
            const forecastData = await forecastResponse.json();

            const skyconIdentifier = getSkyconIdentifier(
                weatherData.weather[0].id,
                weatherData.dt,
                weatherData.sys.sunrise,
                weatherData.sys.sunset
            );

            // Update current weather HTML with canvas and increased text sizes
            weatherInfo.innerHTML = `
              <div class="flex items-start gap-3 mb-2">
                <canvas id="weather-icon-canvas" width="48" height="48"></canvas>
                <div>
                  <div class="flex items-center gap-2 mb-1">
                    <i class="ri-temp-cold-line ri-lg"></i>
                    <span class="text-2xl">${Math.round(weatherData.main.temp)}°C</span>
                  </div>
                  <div class="capitalize text-base">${weatherData.weather[0].description}</div>
                </div>
              </div>
              <div class="flex items-center gap-2 text-base mb-1">
                <i class="ri-drop-line"></i> <span>${weatherData.main.humidity}% humidity</span>
              </div>
              <div class="flex items-center gap-2 text-base">
                <i class="ri-windy-line"></i> <span>${Math.round(weatherData.wind.speed * 3.6)} km/h</span>
              </div>
            `;
            // Add the main icon to the Skycons instance
            skycons.add("weather-icon-canvas", skyconIdentifier);

            const dailyForecasts = {};
            forecastData.list.forEach(item => {
                const date = new Date(item.dt * 1000);
                const dayKey = date.toISOString().split('T')[0]; // YYYY-MM-DD
                if (!dailyForecasts[dayKey] || Math.abs(date.getHours() - 12) < Math.abs(new Date(dailyForecasts[dayKey].dt * 1000).getHours() - 12)) {
                    if (date.getDate() !== new Date().getDate()) {
                        dailyForecasts[dayKey] = item;
                    }
                }
            });
            const nextThreeDays = Object.values(dailyForecasts).slice(0, 3);

            nextThreeDays.forEach((forecast, index) => {
              const date = new Date(forecast.dt * 1000);
              const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });
              const forecastSkyconId = getSkyconIdentifier(forecast.weather[0].id, forecast.dt, weatherData.sys.sunrise, weatherData.sys.sunset);
              const forecastCanvasId = `forecast-icon-${index}`;

              const forecastItem = document.createElement('div');
              forecastItem.className = 'forecast-item items-center';
              // Update forecast HTML with increased text sizes
              forecastItem.innerHTML = `
                <div class="w-1/4 text-left text-base"><span>${dayName}</span></div>
                <div class="w-1/4 flex justify-center"><canvas id="${forecastCanvasId}" width="24" height="24"></canvas></div>
                <div class="w-1/4 text-center capitalize text-sm"><span>${forecast.weather[0].description}</span></div>
                <div class="w-1/4 text-right text-base">
                  <span>${Math.round(forecast.main.temp_max)}°</span> / <span class="text-gray-400">${Math.round(forecast.main.temp_min)}°</span>
                </div>
              `;
              forecastContainer.appendChild(forecastItem);
              skycons.add(forecastCanvasId, forecastSkyconId);
            });
            skycons.play();

        } catch (error) {
            console.error('Error fetching weather:', error);
            // Display error in the main info area if fetch fails
            weatherInfo.innerHTML = `<div class="text-red-400"><i class="ri-error-warning-line mr-2"></i> Failed to load weather data</div>`;
            // Clear any potentially added icons on error
            skycons.remove("weather-icon-canvas");
             for (let i = 0; i < 3; i++) {
                skycons.remove(`forecast-icon-${i}`);
            }
        }
    }

    // Helper function to get Skycon identifier based on weather condition code and time
    function getSkyconIdentifier(code, currentTimeUnix, sunriseUnix, sunsetUnix) {
        const isDay = currentTimeUnix > sunriseUnix && currentTimeUnix < sunsetUnix;

        if (code >= 200 && code < 300) return Skycons.RAIN; // Thunderstorm -> RAIN (or SLEET maybe)
        if (code >= 300 && code < 400) return Skycons.RAIN; // Drizzle -> RAIN
        if (code >= 500 && code < 600) return Skycons.RAIN; // Rain -> RAIN
        if (code >= 600 && code < 700) return Skycons.SNOW; // Snow -> SNOW
        if (code >= 700 && code < 800) return Skycons.FOG;  // Atmosphere -> FOG
        if (code === 800) return isDay ? Skycons.CLEAR_DAY : Skycons.CLEAR_NIGHT; // Clear
        if (code === 801) return isDay ? Skycons.PARTLY_CLOUDY_DAY : Skycons.PARTLY_CLOUDY_NIGHT; // Few clouds
        if (code > 801) return Skycons.CLOUDY; // Scattered, broken, overcast clouds -> CLOUDY

        // Default fallback
        return isDay ? Skycons.CLEAR_DAY : Skycons.CLEAR_NIGHT;
    }

    // --- Contact Form Logic ---
    const contactToggle = document.getElementById('contact-toggle-button');
    const contactOverlay = document.getElementById('contact-overlay');
    const contactClose = document.getElementById('contact-close-button');
    const contactForm = document.getElementById('contact-form');
    const formResult = document.getElementById('form-result');
    const submitButton = document.getElementById('contact-submit-button');

    function closeContactForm() {
        // Add closing animation
        if (submitButton.classList.contains('btn-success')) {
            // If form was successfully submitted, add a final particle burst
            for (let i = 0; i < 10; i++) {
                setTimeout(() => {
                    createParticle(submitButton);
                }, i * 30);
            }
        }

        contactOverlay.classList.remove('active');
        document.body.style.overflow = ''; // Restore background scrolling

        // Clear result message and reset form after animation
        setTimeout(() => {
            formResult.textContent = '';
            formResult.className = 'mt-4 text-sm'; // Reset class
            contactForm.reset();
            submitButton.disabled = false;
            submitButton.textContent = 'Send Message';
            submitButton.classList.remove('btn-success', 'btn-error', 'btn-pulse');
            submitButton.style.animation = '';
        }, 400); // Match CSS transition duration
    }

    if (contactToggle && contactOverlay && contactClose && contactForm) {
        contactToggle.addEventListener('click', openContactForm);
        contactClose.addEventListener('click', closeContactForm);

        // Close on clicking background overlay
        contactOverlay.addEventListener('click', (event) => {
            if (event.target === contactOverlay) { // Ensure click is on overlay itself
                closeContactForm();
            }
        });

        // Close on ESC key
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && contactOverlay.classList.contains('active')) {
                closeContactForm();
            }
        });

        // Create particles function for button click effect
        function createButtonParticles(button) {
            // Create a ripple effect
            const ripple = document.createElement('span');
            ripple.style.position = 'absolute';
            ripple.style.top = '50%';
            ripple.style.left = '50%';
            ripple.style.transform = 'translate(-50%, -50%)';
            ripple.style.width = '0';
            ripple.style.height = '0';
            ripple.style.backgroundColor = 'rgba(255, 255, 255, 0.3)';
            ripple.style.borderRadius = '50%';
            ripple.style.transition = 'all 0.6s ease-out';
            button.appendChild(ripple);

            // Expand the ripple
            setTimeout(() => {
                const buttonWidth = button.offsetWidth;
                const buttonHeight = button.offsetHeight;
                const diameter = Math.max(buttonWidth, buttonHeight) * 2;
                ripple.style.width = `${diameter}px`;
                ripple.style.height = `${diameter}px`;
                ripple.style.opacity = '0';
            }, 10);

            // Remove the ripple after animation completes
            setTimeout(() => {
                if (ripple.parentNode) {
                    ripple.parentNode.removeChild(ripple);
                }
            }, 700);

            // Create flying particles
            const particleCount = 15;
            for (let i = 0; i < particleCount; i++) {
                createParticle(button);
            }
        }

        // Create a single particle
        function createParticle(button) {
            const particle = document.createElement('span');
            particle.className = 'btn-particle';

            // Random size between 5-10px
            const size = Math.random() * 5 + 5;
            particle.style.width = `${size}px`;
            particle.style.height = `${size}px`;

            // Random position within the button
            const buttonRect = button.getBoundingClientRect();
            const offsetX = Math.random() * buttonRect.width;
            const offsetY = Math.random() * buttonRect.height;
            particle.style.left = `${offsetX}px`;
            particle.style.top = `${offsetY}px`;

            // Random direction and distance
            const angle = Math.random() * Math.PI * 2;
            const distance = Math.random() * 100 + 50;
            const tx = Math.cos(angle) * distance;
            const ty = Math.sin(angle) * distance;

            // Set CSS variables for the animation
            particle.style.setProperty('--tx', `${tx}px`);
            particle.style.setProperty('--ty', `${ty}px`);

            // Add to button
            button.appendChild(particle);

            // Animate and remove
            requestAnimationFrame(() => {
                particle.style.animation = `particle-fade 1s ease-out forwards`;
            });

            // Remove particle after animation
            setTimeout(() => {
                if (particle.parentNode) {
                    particle.parentNode.removeChild(particle);
                }
            }, 1000);
        }

        // Handle Form Submission with Fetch API
        contactForm.addEventListener('submit', async function (e) {
            e.preventDefault(); // Prevent default browser submission

            // Trigger the click animation
            createButtonParticles(submitButton);
            submitButton.classList.add('btn-pulse');

            // Remove pulse animation after it completes
            setTimeout(() => {
                submitButton.classList.remove('btn-pulse');
            }, 800);

            submitButton.disabled = true;
            submitButton.textContent = 'Sending...';
            formResult.textContent = ''; // Clear previous result
            formResult.className = 'mt-4 text-sm'; // Reset class

            const formData = new FormData(contactForm);

            // Set timestamp for basic time-based spam detection
            document.getElementById('form-timestamp').value = Date.now().toString();

            // Check for rate limiting using localStorage
            const lastSubmission = localStorage.getItem('lastFormSubmission');
            const now = Date.now();
            if (lastSubmission && (now - parseInt(lastSubmission)) < 60000) { // 1 minute cooldown
                formResult.textContent = 'Please wait a minute before submitting again.';
                formResult.classList.add('text-yellow-400');

                // Warning animation
                submitButton.classList.add('btn-error');
                submitButton.style.animation = 'button-error 0.5s ease-in-out';

                setTimeout(() => {
                    submitButton.style.animation = '';
                    submitButton.classList.remove('btn-error');
                    submitButton.disabled = false;
                    submitButton.textContent = 'Send Message';
                }, 500);
                return;
            }

            try {

                const response = await fetch(contactForm.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                // Check if the response is OK
                if (response.ok) {
                    // Store submission time for rate limiting
                    localStorage.setItem('lastFormSubmission', Date.now().toString());

                    // Success animation
                    submitButton.classList.add('btn-success');
                    submitButton.textContent = 'Sent!';

                    // Create success particles
                    for (let i = 0; i < 20; i++) {
                        setTimeout(() => {
                            createParticle(submitButton);
                        }, i * 50);
                    }

                    // Success animation
                    submitButton.style.animation = 'button-success 0.5s ease-in-out';
                    setTimeout(() => {
                        submitButton.style.animation = '';
                    }, 500);

                    formResult.textContent = 'Message sent successfully!';
                    formResult.classList.add('text-green-400');

                    // Reset form
                    contactForm.reset();

                    // Close form after 2s on success
                    setTimeout(closeContactForm, 2000);
                } else {
                    // Try to get JSON error message
                    let errorMessage = 'Form submission failed.';
                    try {
                        const errorData = await response.json();
                        errorMessage = errorData.error || errorMessage;
                    } catch (e) {
                        console.error('Could not parse error response:', e);
                    }

                    console.error("Form submission error:", errorMessage);

                    formResult.innerHTML = `
                        <div>Error: ${errorMessage}</div>
                        <div class="mt-2">
                            <a href="mailto:<EMAIL>?subject=${encodeURIComponent('Contact from ' + formData.get('name'))}&body=${encodeURIComponent('Project: ' + formData.get('project') + '\n\n' + formData.get('message'))}"
                               class="underline hover:text-blue-400">Try sending email directly</a>
                        </div>
                    `;

                    formResult.classList.add('text-red-400');

                    // Error animation
                    submitButton.classList.add('btn-error');
                    submitButton.style.animation = 'button-error 0.5s ease-in-out';

                    setTimeout(() => {
                        submitButton.style.animation = '';
                        submitButton.classList.remove('btn-error');
                        submitButton.disabled = false; // Re-enable button after animation
                        submitButton.textContent = 'Send Message';
                    }, 500);
                }
            } catch (error) {
                console.error('Network or other error:', error);

                formResult.innerHTML = `
                    <div>A network error occurred. Please try one of these options:</div>
                    <div class="mt-2">
                        <a href="mailto:<EMAIL>?subject=${encodeURIComponent('Contact from ' + formData.get('name'))}&body=${encodeURIComponent('Project: ' + formData.get('project') + '\n\n' + formData.get('message'))}"
                           class="underline hover:text-blue-400">Send email directly</a>
                    </div>
                `;

                formResult.classList.add('text-red-400');

                // Error animation
                submitButton.classList.add('btn-error');
                submitButton.style.animation = 'button-error 0.5s ease-in-out';

                setTimeout(() => {
                    submitButton.style.animation = '';
                    submitButton.classList.remove('btn-error');
                    submitButton.disabled = false; // Re-enable button after animation
                    submitButton.textContent = 'Send Message';
                }, 500);
            }
        });
    }

    // Resources Category Navigation
    const resourcesData = {
        illustrations: {
            title: 'Illustrations',
            description: 'High-quality photos and design inspiration for your projects',
            resources: [
                {
                    name: 'Cosmos',
                    description: 'Elegant and modern visual inspirations.',
                    url: 'https://cosmos.so'
                },
                {
                    name: 'Godly',
                    description: 'Gallery of standout web design and interfaces.',
                    url: 'https://godly.website/'
                },
                {
                    name: 'Unsplash',
                    description: 'Free, high-quality photos for websites and apps.',
                    url: 'https://unsplash.com'
                },
                {
                    name: 'Dribbble',
                    description: 'Design shots, UI inspiration, and creative assets.',
                    url: 'https://dribbble.com'
                },
                {
                    name: 'Awwwards',
                    description: 'Inspiration resource for digital creatives.',
                    url: 'https://www.awwwards.com/'
                }
            ]
        },
        templates: {
            title: 'Templates',
            description: 'Open source templates built by developers, for developers.',
            resources: [
                {
                    name: 'ThemeWagon',
                    description: 'Premium templates for modern websites.',
                    url: 'https://themewagon.com/themes/?swoof=1&pa_price=free'
                },
                {
                    name: 'UIdeck',
                    description: 'High-quality landing page and website templates for startups.',
                    url: 'https://www.uideck.com/templates'
                },
                {
                    name: 'Webflow',
                    description: 'Drag-and-drop, without needing to code.',
                    url: 'https://webflow.com/free-website-templates'
                }
            ]
        },
        design: {
            title: 'Visuals',
            description: 'Creative assets for stock images & videos',
            resources: [
                {
                    name: 'Pexels',
                    description: 'High-quality, copyright-free images and videos.',
                    url: 'https://www.pexels.com/'
                },
                {
                    name: 'Pixabay',
                    description: 'Free stock images & videos for any project.',
                    url: 'https://pixabay.com/'
                },
                {
                    name: 'Behance',
                    description: 'Platform where creative professionals showcase their work.',
                    url: 'https://www.behance.net'
                },
                {
                    name: 'Savee',
                    description: 'Curated inspiration for creative contents.',
                    url: 'https://savee.it/'
                }
            ]
        },
        hosting: {
            title: 'Hosting & Deployment',
            description: 'Reliable hosting providers and services for your websites',
            resources: [
                {
                    name: 'Vercel',
                    description: 'Build and deploy with an AI Cloud.',
                    url: 'https://vercel.com/'
                },
                {
                    name: 'Netlify',
                    description: 'Serverless backend solution for web apps.',
                    url: 'https://netlify.com/'
                },
                {
                    name: 'Github Pages',
                    description: 'Create a website directly from a repository on GitHub.',
                    url: 'https://docs.github.com/en/pages'
                },
                {
                    name: 'Infinity',
                    description: 'Free web hosting with no ads for simple deployments.',
                    url: 'https://www.infinityfree.com'
                },
                {
                    name: 'Zone ID',
                    description: 'Top-notch free domain provider with DNS management.',
                    url: 'https://www.zone.id'
                }
            ]
        },
        'dev-resources': {
            title: 'Toolkits',
            description: 'Utilities to design and build websites',
            resources: [
                {
                    name: 'Visual Studio Code',
                    description: 'Lightweight, opensource and powerful code editor.',
                    url: 'https://code.visualstudio.com/'
                },
                {
                    name: 'Cursor',
                    description: 'AI-first editor to build, refactor and ship faster.',
                    url: 'https://cursor.com'
                },
                {
                    name: 'Windsurf',
                    description: 'Agentic coding platform designed for developers and enterprises.',
                    url: 'https://windsurf.com/'
                },
                {
                    name: 'Notepad++',
                    description: 'Quick edits with syntax highlighting and plugin support.',
                    url: 'https://notepad-plus-plus.org/'
                },
                {
                    name: 'Node.js',
                    description: 'Create servers, web apps, command line tools and scripts.',
                    url: 'https://nodejs.org/'
                },
                {
                    name: 'Auth0',
                    description: 'Secure authentication and authorization platform.',
                    url: 'https://auth0.com/'
                },
                {
                    name: 'Firebase',
                    description: 'BaaS platform offering a comprehensive suite of tools for building and managing apps.',
                    url: 'https://firebase.google.com/'
                }
            ]
        },
        'ai-editors': {
            title: 'Showcase',
            description: 'Projects built by fellow developers. Share your own creations with the community',
            resources: [
                {
                    name: 'WebPortfolios',
                    description: 'Explore stunning portfolios from developers and showcase your own site.',
                    url: 'https://www.webportfolios.dev/'
                },
                {
                    name: 'Web of Devs',
                    description: 'A directory of developers all around the world.',
                    url: 'https://webofdevs.com/'
                },
                {
                    name: 'Siteinspire',
                    description: 'Showcase for finest designs.',
                    url: 'https://www.siteinspire.com/'
                }
            ]
        },
        libraries: {
            title: 'Library',
            description: 'Components, codes and snippets for web development',
            resources: [
                {
                    name: 'CodingNepal',
                    description: 'Practical frontend components, effects, and snippets.',
                    url: 'https://www.codingnepalweb.com'
                },
                {
                    name: 'Animista',
                    description: 'Ready-to-use CSS animations.',
                    url: 'https://animista.net/'
                },
                {
                    name: 'CodePen',
                    description: 'Build, share, and learn JavaScript, CSS, and HTML.',
                    url: 'https://codepen.io/'
                },
                {
                    name: 'React Bits',
                    description: 'Animated UI components For react.',
                    url: 'https://reactbits.dev/'
                },
                {
                    name: 'Flowbite',
                    description: 'Open-source library of UI components.',
                    url: 'https://flowbite.com/'
                },
                {
                    name: 'FreeFrontend',
                    description: 'UI libraries, snippets, and examples.',
                    url: 'https://freefrontend.com'
                }
            ]
        },
        docs: {
            title: 'Docs & References',
            description: 'Helpful tips and platforms to showcase and discover portfolios',
            resources: [
                {
                    name: 'Cursor',
                    description: 'Official docs and guides for the Cursor editor.',
                    url: 'https://docs.cursor.com/en/welcome'
                },
                {
                    name: 'Next.js',
                    description: 'Help for interactive, dynamic, and fast React applications.',
                    url: 'https://nextjs.org/docs'
                },
                {
                    name: 'Github',
                    description: 'Help for your GitHub journey.',
                    url: 'https://docs.github.com/en'
                },
                {
                    name: 'Dev Docs API',
                    description: 'Combined docs for JS frameworks.',
                    url: 'https://devdocs.io/'
                },
                {
                    name: 'Web Dev Resources',
                    description: 'Curated tools and guides for developers.',
                    url: 'https://www.web-dev-resources.com'
                }
            ]
        }
    };

    // Get DOM elements for resources section
    const categoryOverview = document.getElementById('category-overview');
    const categoryDetail = document.getElementById('category-detail');
    const categoryTitle = document.getElementById('category-title');
    const categoryDescription = document.getElementById('category-description');
    const resourcesGrid = document.getElementById('resources-grid');
    const backButton = document.getElementById('back-to-categories');
    const categoryCards = document.querySelectorAll('.category-card');

    // Function to show category details
    function showCategoryDetail(categoryKey) {
        const category = resourcesData[categoryKey];
        if (!category) return;

        // Update content
        categoryTitle.textContent = category.title;
        categoryDescription.textContent = category.description;

        // Clear and populate resources grid
        resourcesGrid.innerHTML = '';
        category.resources.forEach(resource => {
            const resourceCard = document.createElement('a');
            resourceCard.href = resource.url;
            resourceCard.target = '_blank';
            resourceCard.rel = 'noopener noreferrer';
            resourceCard.className = 'resource-card';
            resourceCard.setAttribute('aria-label', `${resource.name} - ${resource.description}`);

            resourceCard.innerHTML = `
                <div class="resource-card-border bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>
                <h4 class="text-xl font-bold mb-2">${resource.name}</h4>
                <p class="theme-text-muted">${resource.description}</p>
                <div class="mt-4 flex items-center text-sm theme-text-muted">
                    <i class="ri-external-link-line mr-2"></i>
                    <span>Visit Resource</span>
                </div>
            `;

            resourcesGrid.appendChild(resourceCard);
        });

        // Animate transition
        categoryOverview.classList.add('fade-out');
        setTimeout(() => {
            categoryOverview.classList.add('hidden');
            categoryDetail.classList.remove('hidden');
            categoryDetail.classList.add('fade-in');
        }, 250);
    }

    // Function to show category overview
    function showCategoryOverview() {
        categoryDetail.classList.add('fade-out');
        categoryDetail.classList.remove('fade-in');
        setTimeout(() => {
            categoryDetail.classList.add('hidden');
            categoryOverview.classList.remove('hidden', 'fade-out');
            categoryOverview.classList.add('fade-in');
        }, 250);
    }

    // Add event listeners to category cards
    categoryCards.forEach(card => {
        card.addEventListener('click', () => {
            const categoryKey = card.getAttribute('data-category');
            showCategoryDetail(categoryKey);
        });

        // Add keyboard support
        card.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                const categoryKey = card.getAttribute('data-category');
                showCategoryDetail(categoryKey);
            }
        });
    });

    // Add event listener to back button
    if (backButton) {
        backButton.addEventListener('click', showCategoryOverview);
    }

}); // End of DOMContentLoaded