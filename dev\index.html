<!DOCTYPE html>
<html lang="en" oncontextmenu="return false">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="google-site-verification" content="F2eWKOuhcGhuftSd2TjSJdV1PWm0GviaFsf9ELztoxI" />
    <meta name="theme-color" content="#000000">
    <meta name="color-scheme" content="dark light">
    <meta name="description" content="Freelance Web Developer delivering responsive websites with modern technologies and innovative solutions. From static front-end pages to fully functional websites, here to guide future projects grow effectively with insights and support. Feel free to send me a message, I'd be happy to connect!">
    <meta name="keywords" content="Yusuf, Freelance, Frontend, Freelancer, Creator, Content, Backend, Nodejs, Nextjs, Developer, Designer, Portfolio, HTML5, CSS, PHP, JavaScript, Node.js">
    <meta name="author" content="<PERSON>">
    <title><PERSON>.</title>

    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="manifest" href="/site.webmanifest">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" rel="stylesheet">

    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/tsparticles@2.12.0/tsparticles.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/skycons/1396634940/skycons.min.js"></script>

    <!-- Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#06020A',
                        secondary: '#4A90E2'
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px',
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px',
                        'button': '8px'
                    }
                }
            }
        }
    </script>

    <!-- Link to external CSS file -->
    <link rel="stylesheet" href="styles.css">


</head>

<body>

    <div id="loading-screen" class="loading-screen">
        <div class="mb-8">
            <img src="images/loading.gif" alt="Loading Icon" style="transform: scale(0.60);">
        </div>
        <div id="loading-bar-container">
            <div id="loading-bar"></div>
        </div>
        <div id="loading-percentage">0%</div>
        <div class="loading-footer">Built with <b><a href="https://code.visualstudio.com/">VSCode</a></b></div>
    </div>

    <!-- Particle Background Container -->
    <div id="tsparticles"></div>

    <aside class="fixed-ui-elements">
        <div class="clock" role="timer" aria-live="polite">
            <span id="time" aria-label="Current time"></span>
            <span id="date" aria-label="Current date"></span>
        </div>

        <button class="theme-toggle" id="theme-toggle" type="button" title="Toggle theme" aria-label="Toggle theme">
            <svg
                xmlns="http://www.w3.org/2000/svg"
                aria-hidden="true"
                width="1em"
                height="1em"
                fill="currentColor"
                stroke-linecap="round"
                class="theme-toggle__classic"
                viewBox="0 0 32 32"
            >
                <defs>
                    <mask id="tt-moon-mask">
                        <rect width="32" height="32" fill="#000" />
                        <circle cx="16" cy="16" r="9.34" fill="#fff" />
                        <circle cx="20" cy="14" r="9.34" fill="#000" />
                    </mask>
                </defs>
                <g class="icon-moon">
                    <circle cx="16" cy="16" r="9.34" mask="url(#tt-moon-mask)" />
                </g>
                <g class="icon-sun">
                    <circle cx="16" cy="16" r="9.34" />
                    <g class="icon-sun-rays" stroke="currentColor" stroke-width="1.5">
                        <path d="M16 5.5v-4" />
                        <path d="M16 30.5v-4" />
                        <path d="M1.5 16h4" />
                        <path d="M26.5 16h4" />
                        <path d="m23.4 8.6 2.8-2.8" />
                        <path d="m5.7 26.3 2.9-2.9" />
                        <path d="m5.8 5.8 2.8 2.8" />
                        <path d="m23.4 23.4 2.9 2.9" />
                    </g>
                </g>
            </svg>
        </button>

        <button class="weather-toggle" aria-label="Toggle weather widget" title="View weather information">
            <i class="ri-cloud-line ri-lg" aria-hidden="true"></i>
        </button>

        <button class="contact-toggle" id="contact-toggle-button" aria-label="Open contact form" title="Contact me">
            <i class="ri-at-line ri-lg" aria-hidden="true"></i>
        </button>

        <button class="spotify-toggle" id="spotify-toggle-button" aria-label="Toggle Spotify playlist" title="Listen to my playlist">
            <i class="ri-spotify-line ri-lg" aria-hidden="true"></i>
        </button>
    </aside>

    <!-- Spotify Widget -->
    <div class="spotify-widget" id="spotify-widget">
        <iframe src="https://open.spotify.com/embed/playlist/6Tjt6XXbRBwSw5OTowp7NL?utm_source=generator&theme=0"
               width="100%" height="380" frameborder="0" allowtransparency="true"
               allow="encrypted-media" loading="lazy">
        </iframe>
    </div>

    <div class="weather-widget">
        <div class="text-xl font-bold mb-2">Montreal, CA</div>
        <div class="weather-info"></div>
        <div class="mt-4">
            <h3 class="text-sm font-semibold mb-2">Forecast</h3>
            <div class="forecast-container"></div>
        </div>
    </div>

    <main class="container mx-auto px-4 py-12 relative z-10">
        <header class="text-center mb-16">
            <div class="profile-border" id="profile-container" role="img" aria-label="Profile image that changes based on theme">
                <img src="images/bg2.png" alt="Yusuf's Profile - Dark Theme" class="dark-mode-image">
                <img src="images/bg1.png" alt="Yusuf's Profile - Light Theme" class="light-mode-image">
            </div>
            <h1 class="text-5xl font-bold mb-4">Yusuf</h1>
            <div class="flex items-center justify-center gap-2 text-base mb-8 theme-text-muted">
                <span>Freelance Web Developer</span>
                <span class="flex items-center justify-center">
                    <span class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
                </span>
            </div>
            <p class="text-lg mb-8 theme-text-muted">Learning, building and gaining skills with every click.</p>
            <div class="flex justify-center gap-6">
                <a href="javascript:void(0);" onclick="openContactForm();" class="hover:text-gray-300 transition-all duration-300 hover:scale-125"><i class="ri-mail-line ri-lg"></i></a>
                <a href="https://www.instagram.com/uzygram" target="_blank" rel="noopener noreferrer" class="hover:text-gray-300 transition-all duration-300 hover:scale-125"><i class="ri-instagram-line ri-lg"></i></a>
                <a href="https://github.com/uzygram" target="_blank" rel="noopener noreferrer" class="hover:text-gray-300 transition-all duration-300 hover:scale-125"><i class="ri-github-line ri-lg"></i></a>
            </div>
            <div class="mt-16">
                <h3 class="text-lg theme-text-muted mb-4 text-center"><b>Experiences with</b></h3>
                <div class="flex justify-center gap-6 flex-wrap">
                    <div class="tech-icon-container">
                        <i class="ri-html5-line ri-2x transition-all duration-300 hover:scale-125 tech-icon"></i>
                        <span class="tech-tooltip">HTML5</span>
                    </div>
                    <div class="tech-icon-container">
                        <i class="ri-css3-line ri-2x transition-all duration-300 hover:scale-125 tech-icon"></i>
                        <span class="tech-tooltip">CSS3</span>
                    </div>
                    <div class="tech-icon-container">
                        <i class="ri-tailwind-css-line ri-2x transition-all duration-300 hover:scale-125 tech-icon"></i>
                        <span class="tech-tooltip">Tailwind CSS</span>
                    </div>
                    <div class="tech-icon-container">
                        <i class="ri-javascript-line ri-2x transition-all duration-300 hover:scale-125 tech-icon"></i>
                        <span class="tech-tooltip">JavaScript</span>
                    </div>
                    <div class="tech-icon-container">
                        <i class="ri-reactjs-line ri-2x transition-all duration-300 hover:scale-125 tech-icon"></i>
                        <span class="tech-tooltip">React</span>
                    </div>
                    <div class="tech-icon-container">
                        <i class="ri-nextjs-line ri-2x transition-all duration-300 hover:scale-125 tech-icon"></i>
                        <span class="tech-tooltip">Next.js</span>
                    </div>
                    <div class="tech-icon-container">
                        <i class="ri-nodejs-line ri-2x transition-all duration-300 hover:scale-125 tech-icon"></i>
                        <span class="tech-tooltip">Node.js</span>
                    </div>
                </div>
            </div>
        </header>

        <section id="services" class="mb-16" aria-labelledby="services-heading">
            <h2 id="services-heading" class="text-2xl font-bold mb-8">+ SERVICES</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="project-card">
                    <div class="border-effect bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>
                    <video src="images/webdev.mp4" autoplay loop muted playsinline disablepictureinpicture class="w-full h-52 object-cover">
                        <source src="images/webdev.mp4" type="video/mp4">
                        Your browser does not support the video tag.
                    </video>
                    <br>
                    <h3 class="text-xl font-bold mb-2">WEB DEVELOPMENT 🪐</h3>
                    <p class="theme-text-muted">Since an early age I've been exploring the digital world with enthusiasm. From front-end pages to fully functional websites, I always aim to bring ideas to life, empowering brands and individual projects to achieve a meaningful growth.</p>
                </div>
                <div class="project-card">
                    <div class="border-effect bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>
                    <video src="images/bg.mp4" autoplay loop muted playsinline disablepictureinpicture class="w-full h-52 object-cover">
                        <source src="images/bg.mp4" type="video/mp4">
                        Your browser does not support the video tag.
                    </video>
                    <br>
                    <h3 class="text-xl font-bold mb-2">UI/UX DESIGN ✨</h3>
                    <p class="theme-text-muted">Addict to clean UI, I love blending aesthetics and bringing futuristic interfaces to reality. I thrive on transforming visionary concepts into digital experiences. Fully in sync with the creators and brands identity.</p>
                </div>
                <div class="project-card">
                    <div class="border-effect bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>
                    <video src="images/portfolio.mp4" autoplay loop muted playsinline disablepictureinpicture class="w-full h-52 object-cover">
                        <source src="images/portfolio.mp4" type="video/mp4">
                        Your browser does not support the video tag.
                    </video>
                    <br>
                    <h3 class="text-xl font-bold mb-2">PORTFOLIO & BRANDING 🚀</h3>
                    <p class="theme-text-muted">I’ll work with you to craft the ideal result that attracts and opens new opportunities. Whether you're a creator, freelancer or entrepreneur, presentation is key and how you present yourself matters.</p>
                </div>
                <div class="project-card">
                    <div class="border-effect bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>
                    <video src="images/strategy.mp4" autoplay loop muted playsinline disablepictureinpicture class="w-full h-52 object-cover">
                        <source src="images/strategy.mp4" type="video/mp4">
                        Your browser does not support the video tag.
                    </video>
                    <br>
                    <h3 class="text-xl font-bold mb-2">PLAN & STRATEGY 🎯</h3>
                    <p class="theme-text-muted">In today’s fast-paced world, having a clear strategy is vital. Refining a model, optimizing its process or developing a brand new plan, I can deliver the insights and support required to guide you through your journey.</p>
                </div>
            </div>
        </section>

        <section id="tools" class="mb-16 mb-24" aria-labelledby="tools-heading">
            <h2 id="tools-heading" class="text-2xl font-bold mb-8">+ MY TOOLS</h2>
            <div class="space-y-6">
                 <div class="service-card">
                    <div class="service-card-border bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>
                    <div class="flex items-center gap-4 mb-2">
                        <i class="ri-codepen-line ri-lg"></i>
                        <h3 class="text-xl font-bold">VISUAL STUDIO CODE</h3>
                    </div>
                    <p class="theme-text-muted">VSCode is my primary environment, providing me an unmatched flexibility that optimizes my coding experience for maximum productivity and comfort.</p>
                </div>
                 <div class="service-card">
                    <div class="service-card-border bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>
                    <div class="flex items-center gap-4 mb-2">
                        <i class="ri-poker-diamonds-line"></i>
                        <h3 class="text-xl font-bold">NETLIFY</h3>
                    </div>
                    <p class="theme-text-muted">Netlify shines with its simplicity in setup, powerful CI/CD automation, and seamless Git integration. I often turn to it when I want fast iteration, serverless functions, all in one unified platform. Makes deployment painless. No server setup, no headaches.</p>
                </div>
                 <div class="service-card">
                     <div class="service-card-border bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>
                    <div class="flex items-center gap-4 mb-2">
                        <i class="ri-github-line ri-lg"></i>
                        <h3 class="text-xl font-bold">GITHUB</h3>
                    </div>
                    <p class="theme-text-muted">GitHub serves as the backbone of my personal development process, with its community and collaborative capabilities, the platform became an indispensable asset for my ecosystem. It became the core of how I build, learn, and share.</p>
                </div>
            </div>
        </section>

        <section id="resources" class="mb-16 mb-24" aria-labelledby="resources-heading">
            <h2 id="resources-heading" class="text-2xl font-bold mb-8">+ DEV VAULT</h2>

            <!-- Category Overview -->
            <div id="category-overview" class="transition-all duration-500 ease-in-out">
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    <!-- Illustrations Category -->
                    <div class="category-card" data-category="illustrations" role="button" tabindex="0" aria-label="Browse Illustrations resources">
                        <div class="category-card-border bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>
                        <div class="category-icon mb-4">
                            <i class="ri-image-line ri-2x"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-2">Illustrations</h3>
                        <p class="theme-text-muted text-sm">High-quality photos and design inspiration</p>
                        <div class="category-count mt-3">
                            <span class="text-xs uppercase tracking-wide theme-text-muted">5 Resources</span>
                        </div>
                    </div>

                    <!-- Web Templates Category -->
                    <div class="category-card" data-category="templates" role="button" tabindex="0" aria-label="Browse Web Templates resources">
                        <div class="category-card-border bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>
                        <div class="category-icon mb-4">
                            <i class="ri-code-s-slash-line ri-2x"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-2">Templates</h3>
                        <p class="theme-text-muted text-sm">Open source templates built by developers, for developers</p>
                        <div class="category-count mt-3">
                            <span class="text-xs uppercase tracking-wide theme-text-muted">3 Resources</span>
                        </div>
                    </div>

                    <!-- Art & Design Category -->
                    <div class="category-card" data-category="design" role="button" tabindex="0" aria-label="Browse Art & Design resources">
                        <div class="category-card-border bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>
                        <div class="category-icon mb-4">
                            <i class="ri-palette-line ri-2x"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-2">Visuals</h3>
                        <p class="theme-text-muted text-sm">Creative assets for stock images & videos</p>
                        <div class="category-count mt-3">
                            <span class="text-xs uppercase tracking-wide theme-text-muted">4 Resources</span>
                        </div>
                    </div>

                    <!-- Web Hosting Category -->
                    <div class="category-card" data-category="hosting" role="button" tabindex="0" aria-label="Browse Web Hosting resources">
                        <div class="category-card-border bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>
                        <div class="category-icon mb-4">
                            <i class="ri-server-line ri-2x"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-2">Hosting & Deployment</h3>
                        <p class="theme-text-muted text-sm">Reliable hosting providers and services</p>
                        <div class="category-count mt-3">
                            <span class="text-xs uppercase tracking-wide theme-text-muted">5 Resources</span>
                        </div>
                    </div>

                    <!-- Dev Resources Category -->
                    <div class="category-card" data-category="dev-resources" role="button" tabindex="0" aria-label="Browse Developer Resources">
                        <div class="category-card-border bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>
                        <div class="category-icon mb-4">
                            <i class="ri-tools-line ri-2x"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-2">Toolkits</h3>
                        <p class="theme-text-muted text-sm">Utilities to design and build websites</p>
                        <div class="category-count mt-3">
                            <span class="text-xs uppercase tracking-wide theme-text-muted">7 Resources</span>
                        </div>
                    </div>

                    <!-- AI & Code Editors Category -->
                    <div class="category-card" data-category="ai-editors" role="button" tabindex="0" aria-label="Browse AI & Code Editors resources">
                        <div class="category-card-border bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>
                        <div class="category-icon mb-4">
                            <i class="ri-pages-fill ri-2x"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-2">Showcase</h3>
                        <p class="theme-text-muted text-sm">Projects built by fellow developers. Share your own creations with the community</p>
                        <div class="category-count mt-3">
                            <span class="text-xs uppercase tracking-wide theme-text-muted">3 Resources</span>
                        </div>
                    </div>

                    <!-- CSS & JS Libraries Category -->
                    <div class="category-card" data-category="libraries" role="button" tabindex="0" aria-label="Browse CSS & JS Libraries resources">
                        <div class="category-card-border bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>
                        <div class="category-icon mb-4">
                            <i class="ri-bookmark-3-fill ri-2x"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-2">Library</h3>
                        <p class="theme-text-muted text-sm">Components, codes and snippets</p>
                        <div class="category-count mt-3">
                            <span class="text-xs uppercase tracking-wide theme-text-muted">6 Resources</span>
                        </div>
                    </div>

                    <!-- Docs & Directory Category -->
                    <div class="category-card" data-category="docs" role="button" tabindex="0" aria-label="Browse Documentation & Directory resources">
                        <div class="category-card-border bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>
                        <div class="category-icon mb-4">
                            <i class="ri-folder-cloud-fill ri-2x"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-2">Docs & References</h3>
                        <p class="theme-text-muted text-sm">Helpful tips and platforms to showcase and discover portfolios</p>
                        <div class="category-count mt-3">
                            <span class="text-xs uppercase tracking-wide theme-text-muted">5 Resources</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Category Detail View -->
            <div id="category-detail" class="hidden transition-all duration-500 ease-in-out">
                <!-- Back Button -->
                <div class="mb-8">
                    <button id="back-to-categories" class="inline-flex items-center gap-2 px-4 py-2 rounded-lg bg-var(--card-bg) hover:bg-var(--card-bg-hover) transition-all duration-300 hover:scale-105" style="background-color: var(--card-bg);" onmouseover="this.style.backgroundColor='var(--card-bg-hover)'" onmouseout="this.style.backgroundColor='var(--card-bg)'">
                        <i class="ri-arrow-left-line"></i>
                        <span>Back to Categories</span>
                    </button>
                </div>

                <!-- Category Title -->
                <div class="mb-8">
                    <h3 id="category-title" class="text-2xl font-bold mb-2"></h3>
                    <p id="category-description" class="theme-text-muted"></p>
                </div>

                <!-- Resources Grid -->
                <div id="resources-grid" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Resources will be populated here -->
                </div>
            </div>
        </section>


        <section id="location" class="mb-16 map-section" aria-labelledby="location-heading">
            <h2 id="location-heading" class="text-2xl font-bold mb-8">+ LOCATION</h2>
            <div class="map-container-wrapper">
                <div class="map-container">
                    <iframe
                        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d89466.98000211321!2d-73.71074746267145!3d45.51323649371029!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x4cc91a4d31166b3d%3A0xe16252d7fe06209e!2sVille-Marie%2C%20Montreal%2C%20QC!5e0!3m2!1sen!2sca!4v1745422664417!5m2!1sen!2sca"
                        allowfullscreen=""
                        loading="lazy"
                        referrerpolicy="no-referrer-when-downgrade"
                        aria-label="Interactive map of Montreal"
                        title="Map of Montreal">
                    </iframe>
                </div>
            </div>
        </section>
    </main>

        <footer class="footer" role="contentinfo">
            <img src="yk.png" alt="Yusuf K. Logo - Dark Theme" class="footer-image dark-mode-image" draggable="false">
            <img src="yk2.png" alt="Yusuf K. Logo - Light Theme" class="footer-image light-mode-image" draggable="false">
            <div class="footer-copyright" aria-label="Copyright information">© 2025 Yusuf K.</div>
        </footer>
    </div>

    <!-- Contact Form Overlay -->
    <div class="contact-overlay" id="contact-overlay">
        <div class="contact-form-container">
            <button class="contact-close-button" id="contact-close-button" title="Close">
                <i class="ri-close-line"></i>
            </button>
            <h2 class="text-2xl font-bold mb-2 text-center">Get In Touch</h2>
            <p class="text-base theme-text-muted mb-6 text-center">
                U have an exciting project in mind or need assistance with? Feel free to shoot me a message — I’d be happy to connect.
            </p>
            <form id="contact-form" action="https://formcarry.com/s/S05h5ApUuLu" method="POST" accept-charset="UTF-8">
                <!-- Formcarry configuration -->
                <input type="hidden" name="_subject" value="Form Submission Yusuf.dev">
                <!-- Honeypot field for spam prevention -->
                <input type="hidden" name="_gotcha" value="">
                <!-- Add CORS headers -->
                <input type="hidden" name="_cors" value="true">

                <div class="mb-4">
                    <label for="name" class="sr-only">Your Name</label>
                    <input type="text" id="name" name="name" placeholder="Your Name" required aria-required="true">
                </div>
                <div class="mb-4">
                    <label for="email" class="sr-only">Your Email</label>
                    <input type="email" id="email" name="email" placeholder="Your Email" required aria-required="true">
                </div>
                <div class="mb-4">
                    <label for="project" class="sr-only">Your Project</label>
                    <input type="text" id="project" name="project" placeholder="Your Project" required aria-required="true">
                </div>
                <div class="mb-4">
                    <label for="message" class="sr-only">Your Message</label>
                    <textarea id="message" name="message" placeholder="Your Message" required aria-required="true"></textarea>
                </div>
                <!-- Formspree doesn't need these fields, but we'll keep a timestamp for our own rate limiting -->
                <input type="hidden" name="timestamp" id="form-timestamp" value="">

                <div class="text-center">
                    <button type="submit" id="contact-submit-button">Send Message</button>
                </div>
                <div id="form-result" class="mt-4 text-sm" role="alert" aria-live="polite"></div>
                <div class="mt-4 text-sm text-center theme-text-muted">
                    If you experience any issues with the form, you can also email me directly at
                    <a href="mailto:<EMAIL>" class="underline hover:text-blue-400" rel="noopener"><EMAIL></a>
                </div>
            </form>
        </div>
    </div>



    <!-- Link to external JavaScript file -->
    <script src="script.js"></script>



</body>
</html>